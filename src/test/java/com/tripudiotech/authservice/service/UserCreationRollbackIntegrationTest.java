/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.service.context.UserCreationContext;
import com.tripudiotech.base.client.dto.request.AccountRequest;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.securitylib.dto.UserInformation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit test for the rollback functionality.
 * This test verifies that the rollback context tracking works correctly.
 */
class UserCreationRollbackIntegrationTest {

    private UserCreationContext testContext;

    @BeforeEach
    void setUp() {
        // Create a test context
        testContext = UserCreationContext.builder()
                .tenantId("test-tenant")
                .companyId("company-123")
                .resolvedEmail("<EMAIL>")
                .token("Bearer test-token")
                .request(AccountRequest.builder()
                        .username("<EMAIL>")
                        .firstName("John")
                        .lastName("Doe")
                        .properties(new HashMap<>())
                        .build())
                .userInformation(UserInformation.builder()
                        .username("admin")
                        .email("<EMAIL>")
                        .enabled(true)
                        .build())
                .build();
    }

    @Test
    @DisplayName("Should create context successfully")
    void shouldCreateContextSuccessfully() {
        assertNotNull(testContext, "UserCreationContext should be created");
        assertNotNull(testContext.getTenantId(), "Tenant ID should be set");
        assertNotNull(testContext.getResolvedEmail(), "Email should be set");
    }

    @Test
    @DisplayName("Should create context with rollback tracking")
    void shouldCreateContextWithRollbackTracking() {
        // Given - fresh context
        assertFalse(testContext.isPersonEntityCreated(), "Person entity should not be marked as created initially");
        assertFalse(testContext.isKeycloakUserCreated(), "Keycloak user should not be marked as created initially");
        assertFalse(testContext.isPermissionsAssigned(), "Permissions should not be marked as assigned initially");
        assertFalse(testContext.requiresRollback(), "Should not require rollback initially");

        // When - marking entities as created
        UserCreationContext contextWithPerson = testContext.withCreatedEntity(createTestEntity());
        UserCreationContext contextWithKeycloak = contextWithPerson.withAuthServerId("auth-123");
        UserCreationContext contextWithPermissions = contextWithKeycloak.withPermissionsAssigned();

        // Then - verify tracking
        assertTrue(contextWithPerson.isPersonEntityCreated(), "Person entity should be marked as created");
        assertFalse(contextWithPerson.isKeycloakUserCreated(), "Keycloak user should not be marked as created yet");
        assertTrue(contextWithPerson.requiresRollback(), "Should require rollback when person entity created");

        assertTrue(contextWithKeycloak.isPersonEntityCreated(), "Person entity should still be marked as created");
        assertTrue(contextWithKeycloak.isKeycloakUserCreated(), "Keycloak user should be marked as created");
        assertTrue(contextWithKeycloak.requiresRollback(), "Should require rollback when both entities created");

        assertTrue(contextWithPermissions.isPersonEntityCreated(), "Person entity should still be marked as created");
        assertTrue(contextWithPermissions.isKeycloakUserCreated(), "Keycloak user should still be marked as created");
        assertTrue(contextWithPermissions.isPermissionsAssigned(), "Permissions should be marked as assigned");
        assertTrue(contextWithPermissions.requiresRollback(), "Should require rollback when everything created");
    }

    @Test
    @DisplayName("Should determine correct rollback requirements")
    void shouldDetermineCorrectRollbackRequirements() {
        // Given - context with different creation states
        UserCreationContext emptyContext = testContext;
        UserCreationContext personOnlyContext = testContext.withCreatedEntity(createTestEntity());
        UserCreationContext personAndKeycloakContext = personOnlyContext.withAuthServerId("auth-123");
        UserCreationContext fullContext = personAndKeycloakContext.withPermissionsAssigned();

        // When & Then - verify rollback requirements
        assertTrue(emptyContext.getRollbackRequirements().isEmpty(),
                "Empty context should have no rollback requirements");

        assertEquals(1, personOnlyContext.getRollbackRequirements().size(),
                "Person-only context should have 1 rollback requirement");
        assertTrue(personOnlyContext.getRollbackRequirements().contains("ROLLBACK_PERSON_ENTITY"),
                "Should require person entity rollback");

        assertEquals(2, personAndKeycloakContext.getRollbackRequirements().size(),
                "Person+Keycloak context should have 2 rollback requirements");
        assertTrue(personAndKeycloakContext.getRollbackRequirements().contains("ROLLBACK_KEYCLOAK_USER"),
                "Should require Keycloak user rollback");
        assertTrue(personAndKeycloakContext.getRollbackRequirements().contains("ROLLBACK_PERSON_ENTITY"),
                "Should require person entity rollback");

        assertEquals(3, fullContext.getRollbackRequirements().size(),
                "Full context should have 3 rollback requirements");
        assertTrue(fullContext.getRollbackRequirements().contains("ROLLBACK_PERMISSIONS"),
                "Should require permissions rollback");
        assertTrue(fullContext.getRollbackRequirements().contains("ROLLBACK_KEYCLOAK_USER"),
                "Should require Keycloak user rollback");
        assertTrue(fullContext.getRollbackRequirements().contains("ROLLBACK_PERSON_ENTITY"),
                "Should require person entity rollback");
    }

    @Test
    @DisplayName("Should generate creation state summary")
    void shouldGenerateCreationStateSummary() {
        // Given - different context states
        UserCreationContext emptyContext = testContext;
        UserCreationContext fullContext = testContext
                .withCreatedEntity(createTestEntity())
                .withAuthServerId("auth-123")
                .withPermissionsAssigned();

        // When & Then - verify summaries
        String emptySummary = emptyContext.getCreationStateSummary();
        assertTrue(emptySummary.contains("PersonEntity: ✗"), "Empty context should show person entity not created");
        assertTrue(emptySummary.contains("KeycloakUser: ✗"), "Empty context should show Keycloak user not created");
        assertTrue(emptySummary.contains("Permissions: ✗"), "Empty context should show permissions not assigned");

        String fullSummary = fullContext.getCreationStateSummary();
        assertTrue(fullSummary.contains("PersonEntity: ✓"), "Full context should show person entity created");
        assertTrue(fullSummary.contains("KeycloakUser: ✓"), "Full context should show Keycloak user created");
        assertTrue(fullSummary.contains("Permissions: ✓"), "Full context should show permissions assigned");
    }

    @Test
    @DisplayName("Should handle rollback action tracking")
    void shouldHandleRollbackActionTracking() {
        // Given - context with rollback actions
        UserCreationContext contextWithActions = testContext
                .withRollbackAction("Created person entity")
                .withRollbackAction("Created Keycloak user")
                .withRollbackAction("Assigned permissions");

        // When & Then - verify action tracking
        assertEquals(3, contextWithActions.getRollbackActions().size(),
                "Should track all rollback actions");
        assertTrue(contextWithActions.getRollbackActions().contains("Created person entity"),
                "Should contain person entity action");
        assertTrue(contextWithActions.getRollbackActions().contains("Created Keycloak user"),
                "Should contain Keycloak user action");
        assertTrue(contextWithActions.getRollbackActions().contains("Assigned permissions"),
                "Should contain permissions action");
    }

    private EntityWithPermission createTestEntity() {
        EntityWithPermission entity = new EntityWithPermission();
        entity.setId("test-entity-123");
        entity.setProperties(new HashMap<>());
        return entity;
    }
}
